# Multimodal Agentic RAG Chatbot - Product Requirements Document (PRD)

## 1. Executive Summary

### Product Vision
A lightweight, intelligent CA assistant chatbot capable of handling legal, tax, business, and compliance queries through a combination of RAG-based knowledge retrieval, document analysis, and real-time web search capabilities.

### Core Value Proposition
- **Multimodal Document Processing**: Analyze PDFs, invoices, images, Excel sheets
- **Intelligent Query Routing**: Agentic system that routes queries to appropriate tools
- **Domain Expertise**: Specialized in CA/legal/tax/business domains via RAG
- **Real-time Information**: Web search for current data and trends
- **Chain of Thought Reasoning**: Complex problem-solving capabilities

## 2. Product Requirements

### 2.1 Functional Requirements

#### Core Capabilities
- **Document Processing**: Support PDF, DOC/DOCX, XLS/XLSX, images (JPG, PNG), TXT files
- **Query Classification**: Automatic routing between RAG, web search, and document analysis
- **Multi-turn Conversations**: Maintain context within single session
- **Reasoning Engine**: Apply chain-of-thought for complex queries
- **Response Generation**: Contextually appropriate responses with citations

#### Domain-Specific Features
- **Tax Compliance**: ITR filing, deductions, tax planning
- **Legal Advisory**: Company law, compliance, documentation
- **Business Consulting**: Financial analysis, business strategies
- **Government Schemes**: Latest schemes, eligibility, application processes

### 2.2 Non-Functional Requirements

#### Performance
- **Response Time**: < 3 seconds for RAG queries, < 5 seconds for web search
- **File Processing**: < 10 seconds for document analysis (up to 10MB)
- **Concurrent Users**: Support 50+ simultaneous users
- **Memory Usage**: < 512MB RAM per active session

#### Scalability
- **Horizontal Scaling**: Stateless architecture for easy scaling
- **Resource Optimization**: Efficient memory management
- **Load Balancing**: Support for multiple backend instances

## 3. Technical Architecture

### 3.1 System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend UI   │    │  Agent Router   │    │  RAG System     │
│  (Streamlit/    │◄──►│  (Query Class.  │◄──►│  (Qdrant +      │
│   Flask)        │    │   & Routing)    │    │   OpenAI)       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                    ┌───────────┼───────────┐
                    ▼           ▼           ▼
            ┌─────────────┐ ┌─────────────┐ ┌─────────────┐
            │Web Search   │ │Document     │ │Session      │
            │Tool         │ │Analysis     │ │Management   │
            │(Brave API)  │ │Tool         │ │             │
            └─────────────┘ └─────────────┘ └─────────────┘
```

### 3.2 Technology Stack

#### Backend
- **Framework**: Flask (lightweight) or FastAPI (async support)
- **Language**: Python 3.9+
- **LLM Integration**: OpenAI API (GPT-4)
- **Vector Database**: Qdrant Cloud
- **Document Processing**: PyPDF2, python-docx, openpyxl, Pillow, pytesseract
- **Web Search**: Brave Search API
- **Session Management**: Redis (optional) or in-memory

#### Frontend
- **Primary**: Streamlit (rapid development)
- **Alternative**: Flask + HTML/CSS/JS
- **File Upload**: Streamlit file uploader or Flask-WTF

#### Dependencies
```python
# Core Dependencies
flask==2.3.3
streamlit==1.28.0
openai==1.3.0
qdrant-client==1.6.0
requests==2.31.0

# Document Processing
PyPDF2==3.0.1
python-docx==0.8.11
openpyxl==3.1.2
Pillow==10.1.0
pytesseract==0.3.10

# Utilities
python-dotenv==1.0.0
langchain==0.0.335
tiktoken==0.5.1
```

## 4. Detailed Technical Specifications

### 4.1 Agent Router Architecture

```python
class AgentRouter:
    def __init__(self):
        self.rag_system = RAGSystem()
        self.web_search = WebSearchTool()
        self.doc_analyzer = DocumentAnalyzer()
        self.session_manager = SessionManager()
    
    def route_query(self, query: str, context: dict, uploaded_document: dict = None) -> str:
        """
        Route query to appropriate tool based on classification with document handling
        """
        # Special handling for document-related queries
        if uploaded_document and self.is_document_query(query):
            return self.handle_document_query(query, context, uploaded_document)
        
        # Standard query routing
        query_type = self.classify_query(query)
        
        if query_type == "rag_domain":
            return self.rag_system.query(query, context)
        elif query_type == "web_search":
            return self.web_search.search(query)
        elif query_type == "document_analysis":
            return self.doc_analyzer.analyze(query, context)
        else:
            return self.handle_general_query(query)
    
    def is_document_query(self, query: str) -> bool:
        """
        Check if query is asking about the uploaded document
        """
        document_indicators = [
            'this document', 'this file', 'this pdf', 'this invoice', 'this bill',
            'uploaded document', 'attached file', 'in this', 'from this',
            'analyze this', 'what is this', 'explain this', 'summarize this'
        ]
        query_lower = query.lower()
        return any(indicator in query_lower for indicator in document_indicators)
    
    def handle_document_query(self, query: str, context: dict, uploaded_document: dict) -> str:
        """
        Handle document-specific queries with RAG priority
        """
        # Use document analyzer with RAG priority
        return self.doc_analyzer.analyze_document_with_query(
            uploaded_document['file_path'], 
            query, 
            context
        )
```

### 4.2 Query Classification System

#### Classification Categories with Document Priority
1. **RAG Domain Queries with Document Context**
   - Document relevance assessment first
   - High relevance: RAG knowledge + document context
   - Medium relevance: Hybrid approach (RAG + LLM analysis)
   - Low relevance: LLM-only analysis with CA context

2. **Standard RAG Domain Queries**
   - Tax-related: "What are the latest tax deductions for AY 2024-25?"
   - Legal: "What are the compliance requirements for private companies?"
   - Business: "How to register a partnership firm?"

3. **Web Search Queries**
   - Current events: "Best stocks to buy now"
   - Real-time data: "Current GST rates"
   - General knowledge: "Who is the richest person?"

4. **Document Analysis Queries**
   - With uploaded documents: "Analyze this invoice" → RAG Priority
   - Follow-up on documents: "What's the total amount in this bill?" → RAG Priority

#### Enhanced Classification Logic with Document Context
```python
def classify_query(self, query: str, has_document: bool = False, document_relevance: float = 0.0) -> str:
    """
    Classify query using keyword matching, context analysis, and document relevance
    """
    # Domain-specific keywords
    rag_keywords = ['tax', 'legal', 'compliance', 'gst', 'itr', 'company law']
    web_keywords = ['current', 'latest', 'now', 'today', 'best', 'top']
    
    # Document query handling with RAG priority
    if has_document and self.is_document_query(query):
        if document_relevance > 0.7:
            return "rag_domain_with_document"  # High relevance - RAG priority
        elif document_relevance > 0.3:
            return "hybrid_document_analysis"  # Medium relevance - Hybrid approach
        else:
            return "llm_document_analysis"     # Low relevance - LLM only
    
    # Standard query classification
    elif any(keyword in query.lower() for keyword in web_keywords):
        return "web_search"
    elif any(keyword in query.lower() for keyword in rag_keywords):
        return "rag_domain"
    else:
        return "general"

def is_document_query(self, query: str) -> bool:
    """
    Enhanced document query detection
    """
    document_indicators = [
        'this document', 'this file', 'this pdf', 'this invoice', 'this bill',
        'uploaded document', 'attached file', 'in this', 'from this',
        'analyze this', 'what is this', 'explain this', 'summarize this',
        'calculate from this', 'extract from this', 'details in this'
    ]
    query_lower = query.lower()
    return any(indicator in query_lower for indicator in document_indicators)
```

### 4.3 RAG System Integration

#### Qdrant Integration with Document Context Priority
```python
class RAGSystem:
    def __init__(self):
        self.qdrant_client = QdrantClient(
            url="https://your-qdrant-cloud-url",
            api_key="your-qdrant-api-key"
        )
        self.openai_client = OpenAI(api_key="your-openai-api-key")
    
    def query(self, query: str, context: dict) -> str:
        # 1. Generate query embedding
        query_embedding = self.get_embedding(query)
        
        # 2. Search similar documents
        search_results = self.qdrant_client.search(
            collection_name="ca_knowledge_base",
            query_vector=query_embedding,
            limit=5
        )
        
        # 3. Prepare context for LLM
        context_docs = [result.payload['content'] for result in search_results]
        
        # 4. Generate response using chain of thought
        response = self.generate_response(query, context_docs, context)
        
        return response
    
    def query_with_document_context(self, query: str, document_content: str, context: dict) -> str:
        """
        Enhanced RAG query that prioritizes domain knowledge with document context
        """
        # 1. Generate query embedding
        query_embedding = self.get_embedding(query)
        
        # 2. Search similar documents from knowledge base
        search_results = self.qdrant_client.search(
            collection_name="ca_knowledge_base",
            query_vector=query_embedding,
            limit=5
        )
        
        # 3. Prepare context with RAG knowledge as PRIMARY source
        context_docs = [result.payload['content'] for result in search_results]
        
        # 4. Generate response prioritizing RAG knowledge
        response = self.generate_response_with_document_context(
            query, context_docs, document_content, context
        )
        
        return response
    
    def generate_response_with_document_context(self, query: str, rag_docs: list, 
                                               document_content: str, context: dict) -> str:
        """
        Generate response prioritizing RAG knowledge with document as supplementary context
        """
        # Prepare enhanced prompt that prioritizes RAG knowledge
        prompt = f"""
        You are a professional CA assistant with expertise in tax, legal, and business matters.
        
        INSTRUCTIONS:
        1. PRIMARILY use information from the Knowledge Base (most authoritative)
        2. Use the uploaded document as SUPPLEMENTARY context only
        3. If Knowledge Base has relevant information, lead with that
        4. Cross-reference document details with established knowledge
        5. If document contradicts knowledge base, note the discrepancy
        6. Always maintain professional CA advisory tone
        
        KNOWLEDGE BASE INFORMATION:
        {self.format_rag_docs(rag_docs)}
        
        UPLOADED DOCUMENT CONTEXT:
        {document_content[:1500]}...
        
        USER QUERY: {query}
        
        PREVIOUS CONVERSATION CONTEXT:
        {self.format_conversation_context(context)}
        
        Please provide a comprehensive answer that:
        - Leads with authoritative knowledge base information
        - References specific document details where relevant
        - Explains any discrepancies between sources
        - Provides actionable CA advice
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a professional CA assistant. Always prioritize established knowledge base information over document content when providing advice."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3,
            max_tokens=1000
        )
        
        return response.choices[0].message.content
    
    def format_rag_docs(self, docs: list) -> str:
        """Format RAG documents for prompt"""
        formatted_docs = ""
        for i, doc in enumerate(docs, 1):
            formatted_docs += f"Source {i}: {doc[:500]}...\n\n"
        return formatted_docs
    
    def format_conversation_context(self, context: dict) -> str:
        """Format conversation context for prompt"""
        if not context.get('context'):
            return "No previous context"
        
        recent_context = context['context'][-3:]  # Last 3 exchanges
        formatted_context = ""
        for exchange in recent_context:
            formatted_context += f"Q: {exchange['query']}\nA: {exchange['response'][:200]}...\n\n"
        return formatted_context
```

### 4.4 Document Analysis System

#### Multi-format Document Processing with RAG Priority
```python
class DocumentAnalyzer:
    def __init__(self, rag_system):
        self.supported_formats = ['.pdf', '.docx', '.xlsx', '.jpg', '.png', '.txt']
        self.rag_system = rag_system
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
    
    def analyze_document_with_query(self, file_path: str, query: str, context: dict) -> str:
        """
        Analyze document and answer query using RAG-first approach
        """
        # 1. Extract document content
        document_data = self.analyze_document(file_path)
        document_content = document_data['content']
        
        # 2. Determine document relevance to domain
        relevance_score = self.assess_document_relevance(document_content)
        
        # 3. Apply RAG-first strategy
        if relevance_score > 0.7:  # High relevance to CA domain
            return self.answer_with_rag_priority(query, document_content, context)
        elif relevance_score > 0.3:  # Medium relevance
            return self.answer_with_hybrid_approach(query, document_content, context)
        else:  # Low relevance - use LLM only
            return self.answer_with_llm_only(query, document_content, context)
    
    def assess_document_relevance(self, document_content: str) -> float:
        """
        Assess document relevance to CA domain (tax, legal, business)
        Returns score between 0-1
        """
        ca_keywords = [
            'tax', 'gst', 'income tax', 'tds', 'tcs', 'itr', 'audit', 'compliance',
            'balance sheet', 'profit loss', 'financial statement', 'invoice', 'bill',
            'company law', 'partnership', 'proprietorship', 'corporation', 'legal',
            'agreement', 'contract', 'mou', 'deed', 'registration', 'license',
            'accounting', 'bookkeeping', 'ledger', 'journal', 'voucher', 'receipt',
            'salary', 'wages', 'allowance', 'deduction', 'exemption', 'section',
            'act', 'rule', 'notification', 'circular', 'amendment', 'form'
        ]
        
        # Convert to lowercase for matching
        content_lower = document_content.lower()
        
        # Count keyword matches
        keyword_matches = sum(1 for keyword in ca_keywords if keyword in content_lower)
        
        # Calculate relevance score
        relevance_score = min(keyword_matches / len(ca_keywords) * 5, 1.0)
        
        return relevance_score
    
    def answer_with_rag_priority(self, query: str, document_content: str, context: dict) -> str:
        """
        Answer query primarily using RAG with document as additional context
        """
        # 1. First, try to answer using RAG system
        try:
            rag_response = self.rag_system.query_with_document_context(
                query, document_content, context
            )
            
            # 2. If RAG provides substantial answer, use it
            if len(rag_response.strip()) > 50:  # Substantial response
                return f"Based on my knowledge base and your document:\n\n{rag_response}"
            else:
                # 3. Fallback to hybrid approach
                return self.answer_with_hybrid_approach(query, document_content, context)
                
        except Exception as e:
            # 4. If RAG fails, use hybrid approach
            return self.answer_with_hybrid_approach(query, document_content, context)
    
    def answer_with_hybrid_approach(self, query: str, document_content: str, context: dict) -> str:
        """
        Combine RAG knowledge with document analysis
        """
        # 1. Get RAG response
        try:
            rag_response = self.rag_system.query(query, context)
        except:
            rag_response = "No relevant information found in knowledge base."
        
        # 2. Analyze document with LLM
        llm_response = self.analyze_document_with_llm(query, document_content)
        
        # 3. Combine responses intelligently
        combined_prompt = f"""
        You are a CA assistant. Answer the user's query by combining information from:
        
        1. Knowledge Base Response: {rag_response}
        2. Document Analysis: {llm_response}
        
        User Query: {query}
        
        Provide a comprehensive answer that:
        - Prioritizes knowledge base information when available
        - Supplements with document-specific details
        - Clearly indicates sources of information
        - Maintains professional CA advisory tone
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a professional CA assistant."},
                {"role": "user", "content": combined_prompt}
            ],
            temperature=0.3
        )
        
        return response.choices[0].message.content
    
    def answer_with_llm_only(self, query: str, document_content: str, context: dict) -> str:
        """
        Answer using LLM only when document is not relevant to CA domain
        """
        prompt = f"""
        You are a CA assistant. The user has uploaded a document that appears to be outside 
        the typical CA domain (tax, legal, business). However, they have asked a question 
        about it.
        
        Document Content: {document_content[:2000]}...
        
        User Query: {query}
        
        Please answer their question about the document while noting that this appears to be 
        outside your primary expertise area. If possible, relate it back to any CA-relevant 
        aspects.
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a professional CA assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.3
        )
        
        return response.choices[0].message.content
    
    def analyze_document(self, file_path: str) -> dict:
        """
        Extract content from various document formats
        """
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == '.pdf':
            return self.process_pdf(file_path)
        elif file_extension == '.docx':
            return self.process_docx(file_path)
        elif file_extension == '.xlsx':
            return self.process_excel(file_path)
        elif file_extension in ['.jpg', '.png']:
            return self.process_image(file_path)
        elif file_extension == '.txt':
            return self.process_text(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_extension}")
    
    def process_pdf(self, file_path: str) -> dict:
        """Extract text from PDF"""
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            for page in reader.pages:
                text += page.extract_text()
        
        return {
            'content': text,
            'type': 'pdf',
            'metadata': {'pages': len(reader.pages)}
        }
```

### 4.5 Web Search Integration

#### Brave Search Implementation
```python
class WebSearchTool:
    def __init__(self):
        self.brave_api_key = os.getenv('BRAVE_API_KEY')
        self.base_url = "https://api.search.brave.com/res/v1/web/search"
    
    def search(self, query: str, count: int = 5) -> str:
        """
        Perform web search and generate response
        """
        headers = {
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip',
            'X-Subscription-Token': self.brave_api_key
        }
        
        params = {
            'q': query,
            'count': count,
            'offset': 0,
            'mkt': 'en-IN'  # India market
        }
        
        response = requests.get(self.base_url, headers=headers, params=params)
        search_results = response.json()
        
        # Process search results and generate response
        return self.generate_web_response(query, search_results)
```

### 4.6 Chain of Thought Reasoning

#### Reasoning Framework
```python
class ChainOfThoughtReasoning:
    def __init__(self, openai_client):
        self.openai_client = openai_client
    
    def apply_reasoning(self, query: str, context: dict) -> str:
        """
        Apply chain of thought reasoning for complex queries
        """
        reasoning_prompt = f"""
        You are a CA assistant with expertise in tax, legal, and business matters.
        
        Query: {query}
        Context: {context}
        
        Please think step by step:
        1. What is the user asking?
        2. What information do I need to answer this?
        3. What are the key considerations?
        4. What is my reasoning process?
        5. What is the final answer?
        
        Provide a detailed response with clear reasoning.
        """
        
        response = self.openai_client.chat.completions.create(
            model="gpt-4",
            messages=[
                {"role": "system", "content": "You are a professional CA assistant."},
                {"role": "user", "content": reasoning_prompt}
            ],
            temperature=0.3
        )
        
        return response.choices[0].message.content
```

### 4.7 Session Management

#### In-Memory Session Storage
```python
class SessionManager:
    def __init__(self):
        self.sessions = {}
        self.max_session_age = 3600  # 1 hour
    
    def create_session(self, session_id: str) -> dict:
        """Create new session with empty context"""
        session = {
            'id': session_id,
            'context': [],
            'documents': [],
            'created_at': time.time()
        }
        self.sessions[session_id] = session
        return session
    
    def update_context(self, session_id: str, query: str, response: str):
        """Update session context for continuity"""
        if session_id in self.sessions:
            self.sessions[session_id]['context'].append({
                'query': query,
                'response': response,
                'timestamp': time.time()
            })
    
    def cleanup_expired_sessions(self):
        """Remove expired sessions"""
        current_time = time.time()
        expired_sessions = [
            sid for sid, session in self.sessions.items()
            if current_time - session['created_at'] > self.max_session_age
        ]
        for sid in expired_sessions:
            del self.sessions[sid]
```

## 5. User Interface Design

### 5.1 Streamlit Implementation

```python
import streamlit as st
from chatbot import MultimodalAgenticRAGChatbot

def main():
    st.set_page_config(
        page_title="CA Assistant Chatbot",
        page_icon="🤖",
        layout="wide"
    )
    
    st.title("🤖 CA Assistant - Your Legal, Tax & Business Advisor")
    
    # Initialize chatbot
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = MultimodalAgenticRAGChatbot()
    
    # File upload section
    uploaded_file = st.file_uploader(
        "Upload Document (PDF, DOC, XLS, Images)",
        type=['pdf', 'docx', 'xlsx', 'jpg', 'png', 'txt']
    )
    
    # Chat interface
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # User input
    if prompt := st.chat_input("Ask me anything about tax, legal, or business matters..."):
        # Add user message to chat
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = st.session_state.chatbot.process_query(
                    prompt, 
                    uploaded_file=uploaded_file
                )
            st.markdown(response)
        
        # Add assistant response to chat
        st.session_state.messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()
```

### 5.2 Flask Alternative

```python
from flask import Flask, request, jsonify, render_template
import uuid

app = Flask(__name__)
chatbot = MultimodalAgenticRAGChatbot()

@app.route('/')
def index():
    return render_template('chat.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.json
        query = data.get('query')
        session_id = data.get('session_id', str(uuid.uuid4()))
        
        response = chatbot.process_query(query, session_id=session_id)
        
        return jsonify({
            'response': response,
            'session_id': session_id,
            'status': 'success'
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'status': 'error'
        }), 500

@app.route('/upload', methods=['POST'])
def upload_document():
    try:
        file = request.files['document']
        session_id = request.form.get('session_id')
        
        # Process document
        result = chatbot.process_document(file, session_id)
        
        return jsonify({
            'message': 'Document processed successfully',
            'analysis': result,
            'status': 'success'
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'status': 'error'
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
```

## 6. Main Chatbot Implementation

### 6.1 Core Chatbot Class

```python
class MultimodalAgenticRAGChatbot:
    def __init__(self):
        self.agent_router = AgentRouter()
        self.session_manager = SessionManager()
        self.reasoning_engine = ChainOfThoughtReasoning(
            openai_client=OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        )
    
    def process_query(self, query: str, session_id: str = None, uploaded_file=None) -> str:
        """
        Main query processing pipeline with RAG-first document handling
        """
        # Create or get session
        if not session_id:
            session_id = str(uuid.uuid4())
        
        session = self.session_manager.get_or_create_session(session_id)
        
        # Process uploaded document if provided
        uploaded_document = None
        if uploaded_file:
            doc_analysis = self.process_document(uploaded_file, session_id)
            uploaded_document = doc_analysis
            session['documents'].append(doc_analysis)
        
        # Route query with document context
        response = self.agent_router.route_query(query, session, uploaded_document)
        
        # Apply reasoning for complex queries
        if self.is_complex_query(query):
            response = self.reasoning_engine.apply_reasoning(query, session)
        
        # Update session context
        self.session_manager.update_context(session_id, query, response)
        
        return response
    
    def process_document(self, uploaded_file, session_id: str) -> dict:
        """
        Process uploaded document and prepare for analysis
        """
        # Save uploaded file temporarily
        temp_file_path = f"temp_{session_id}_{uploaded_file.name}"
        
        with open(temp_file_path, "wb") as f:
            f.write(uploaded_file.getbuffer())
        
        # Analyze document
        doc_data = self.agent_router.doc_analyzer.analyze_document(temp_file_path)
        
        # Add file path for later reference
        doc_data['file_path'] = temp_file_path
        doc_data['original_name'] = uploaded_file.name
        
        # Clean up temporary file after analysis
        # os.remove(temp_file_path)  # Commented out to keep file for query processing
        
        return doc_data
    
    def is_complex_query(self, query: str) -> bool:
        """
        Determine if query requires chain of thought reasoning
        """
        complex_indicators = [
            'compare', 'analyze', 'calculate', 'plan', 'strategy',
            'pros and cons', 'best approach', 'step by step'
        ]
        return any(indicator in query.lower() for indicator in complex_indicators)
```

## 7. Deployment & Configuration

### 7.1 Environment Configuration

```bash
# .env file
OPENAI_API_KEY=your_openai_api_key_here
QDRANT_URL=https://your-qdrant-cloud-url
QDRANT_API_KEY=your_qdrant_api_key_here
BRAVE_API_KEY=your_brave_search_api_key_here
FLASK_ENV=production
MAX_SESSION_AGE=3600
MAX_FILE_SIZE=10485760  # 10MB
```

### 7.2 Docker Configuration

```dockerfile
FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    tesseract-ocr \
    tesseract-ocr-eng \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose port
EXPOSE 8501

# Run Streamlit app
CMD ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

### 7.3 Requirements.txt

```txt
streamlit==1.28.0
openai==1.3.0
qdrant-client==1.6.0
requests==2.31.0
PyPDF2==3.0.1
python-docx==0.8.11
openpyxl==3.1.2
Pillow==10.1.0
pytesseract==0.3.10
python-dotenv==1.0.0
langchain==0.0.335
tiktoken==0.5.1
flask==2.3.3
uuid==1.30
```

## 8. Performance Optimization

### 8.1 Caching Strategy

```python
from functools import lru_cache
import hashlib

class CacheManager:
    def __init__(self):
        self.cache = {}
        self.max_cache_size = 1000
    
    @lru_cache(maxsize=100)
    def get_embedding(self, text: str):
        """Cache embeddings for repeated queries"""
        return self.openai_client.embeddings.create(
            input=text,
            model="text-embedding-ada-002"
        ).data[0].embedding
    
    def cache_response(self, query: str, response: str):
        """Cache responses for identical queries"""
        query_hash = hashlib.md5(query.encode()).hexdigest()
        if len(self.cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_key = next(iter(self.cache))
            del self.cache[oldest_key]
        
        self.cache[query_hash] = response
```

### 8.2 Asynchronous Processing

```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class AsyncChatbot:
    def __init__(self):
        self.executor = ThreadPoolExecutor(max_workers=4)
    
    async def process_query_async(self, query: str, session_id: str):
        """
        Process query asynchronously for better performance
        """
        loop = asyncio.get_event_loop()
        
        # Run heavy operations in thread pool
        response = await loop.run_in_executor(
            self.executor,
            self.process_query,
            query,
            session_id
        )
        
        return response
```

## 9. Error Handling & Logging

### 9.1 Comprehensive Error Handling

```python
import logging
from typing import Optional

class ErrorHandler:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.setup_logging()
    
    def setup_logging(self):
        """Configure logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('chatbot.log'),
                logging.StreamHandler()
            ]
        )
    
    def handle_error(self, error: Exception, context: dict) -> str:
        """
        Handle and log errors gracefully
        """
        self.logger.error(f"Error occurred: {str(error)}", extra=context)
        
        if isinstance(error, OpenAIError):
            return "I'm experiencing technical difficulties. Please try again."
        elif isinstance(error, QdrantError):
            return "Knowledge base temporarily unavailable. Please retry."
        elif isinstance(error, FileProcessingError):
            return "Unable to process the uploaded document. Please check format."
        else:
            return "An unexpected error occurred. Please try again."
```

## 10. Testing Strategy

### 10.1 Unit Tests with Document RAG Priority

```python
import unittest
from unittest.mock import Mock, patch

class TestChatbot(unittest.TestCase):
    def setUp(self):
        self.chatbot = MultimodalAgenticRAGChatbot()
    
    def test_query_classification(self):
        """Test query classification logic"""
        # RAG domain query
        query = "What are the tax deductions for AY 2024-25?"
        result = self.chatbot.agent_router.classify_query(query)
        self.assertEqual(result, "rag_domain")
        
        # Web search query
        query = "Who is the richest person now?"
        result = self.chatbot.agent_router.classify_query(query)
        self.assertEqual(result, "web_search")
    
    def test_document_relevance_assessment(self):
        """Test document relevance assessment for RAG priority"""
        # High relevance document (tax invoice)
        tax_content = "GST Invoice No: INV001 Date: 01/04/2024 GSTIN: 29ABCDE1234F1Z5 Taxable Amount: 10000 CGST: 900 SGST: 900 Total: 11800"
        relevance = self.chatbot.agent_router.doc_analyzer.assess_document_relevance(tax_content)
        self.assertGreater(relevance, 0.7)
        
        # Low relevance document (recipe)
        recipe_content = "Ingredients: flour, sugar, eggs, butter. Instructions: Mix all ingredients and bake for 30 minutes."
        relevance = self.chatbot.agent_router.doc_analyzer.assess_document_relevance(recipe_content)
        self.assertLess(relevance, 0.3)
    
    def test_document_query_with_rag_priority(self):
        """Test document query handling with RAG priority"""
        # Mock RAG system
        with patch.object(self.chatbot.agent_router.rag_system, 'query_with_document_context') as mock_rag:
            mock_rag.return_value = "Based on tax regulations, this invoice shows..."
            
            # Test high relevance document query
            query = "What are the tax implications of this invoice?"
            document_data = {
                'content': 'GST Invoice CGST SGST tax amount',
                'file_path': 'test.pdf',
                'relevance': 0.8
            }
            
            result = self.chatbot.agent_router.handle_document_query(query, {}, document_data)
            
            # Should use RAG priority
            mock_rag.assert_called_once()
            self.assertIn("Based on", result)
    
    @patch('openai.OpenAI')
    def test_rag_query_with_document_context(self, mock_openai):
        """Test RAG query processing with document context"""
        mock_openai.return_value.chat.completions.create.return_value.choices[0].message.content = "Test response with document context"
        
        query = "What is GST?"
        document_content = "GST Invoice details..."
        result = self.chatbot.agent_router.rag_system.query_with_document_context(query, document_content, {})
        
        self.assertIsInstance(result, str)
        self.assertGreater(len(result), 0)
    
    def test_irrelevant_document_handling(self):
        """Test handling of irrelevant documents"""
        # Mock LLM response for irrelevant document
        with patch.object(self.chatbot.agent_router.doc_analyzer, 'answer_with_llm_only') as mock_llm:
            mock_llm.return_value = "This appears to be outside my primary expertise area..."
            
            query = "What is this document about?"
            irrelevant_content = "This is a cooking recipe with no CA relevance"
            
            result = self.chatbot.agent_router.doc_analyzer.answer_with_llm_only(query, irrelevant_content, {})
            
            mock_llm.assert_called_once()
            self.assertIn("outside", result)
```

## 11. Security Considerations

### 11.1 Data Protection

```python
import hashlib
import secrets

class SecurityManager:
    def __init__(self):
        self.salt = secrets.token_hex(16)
    
    def sanitize_input(self, user_input: str) -> str:
        """Sanitize user input to prevent injection attacks"""
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '&', '"', "'", '\\', '/', '\n', '\r']
        for char in dangerous_chars:
            user_input = user_input.replace(char, '')
        
        return user_input.strip()
    
    def hash_session_id(self, session_id: str) -> str:
        """Hash session ID for security"""
        return hashlib.sha256(f"{session_id}{self.salt}".encode()).hexdigest()
```

## 12. Monitoring & Analytics

### 12.1 Usage Analytics

```python
class Analytics:
    def __init__(self):
        self.metrics = {
            'total_queries': 0,
            'rag_queries': 0,
            'web_searches': 0,
            'document_analyses': 0,
            'session_count': 0
        }
    
    def track_query(self, query_type: str):
        """Track query types for analytics"""
        self.metrics['total_queries'] += 1
        self.metrics[f'{query_type}_queries'] += 1
    
    def get_metrics(self) -> dict:
        """Get current metrics"""
        return self.metrics.copy()
```

## 13. Roadmap & Future Enhancements

### Phase 1 (MVP)
- Basic RAG functionality
- Document processing (PDF, DOCX, XLSX)
- Web search integration
- Simple UI with Streamlit

### Phase 2 (Enhanced Features)
- Advanced reasoning capabilities
- Better document analysis with OCR
- Voice input/output
- Multi-language support

### Phase 3 (Enterprise Features)
- User authentication
- Organization-specific knowledge bases
- API access for third-party integrations
- Advanced analytics dashboard

## 14. Conclusion

This comprehensive PRD outlines the complete architecture and implementation strategy for a Multimodal Agentic RAG Chatbot specifically designed for CA assistants. The system leverages cutting-edge AI technologies while maintaining focus on performance, scalability, and user experience.

### Key Success Factors
1. **Modular Architecture**: Easy to maintain and extend
2. **Performance Optimization**: Fast response times and efficient resource usage
3. **User-Centric Design**: Intuitive interface with powerful capabilities
4. **Robust Error Handling**: Graceful failure management
5. **Security Focus**: Data protection and input sanitization

The implementation provides a solid foundation for a production-ready CA assistant chatbot that can handle complex legal, tax, and business queries while maintaining the flexibility to adapt to changing requirements.