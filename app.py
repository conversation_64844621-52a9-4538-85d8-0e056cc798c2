"""
Streamlit App for Multimodal Agentic RAG Chatbot
CA Assistant with RAG-first document analysis and intelligent fallback
"""

import streamlit as st
import uuid
import time
import os
from typing import Optional

# Import chatbot components
from chatbot import MultimodalAgenticRAGChatbot

# Page configuration
st.set_page_config(
    page_title="CA Assistant - RAG-First Chatbot",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    .feature-box {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .upload-section {
        border: 2px dashed #1f77b4;
        border-radius: 0.5rem;
        padding: 1rem;
        text-align: center;
        margin: 1rem 0;
    }
    .chat-message {
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 0.5rem;
    }
    .user-message {
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
    }
    .assistant-message {
        background-color: #f3e5f5;
        border-left: 4px solid #9c27b0;
    }
</style>
""", unsafe_allow_html=True)

def initialize_session_state():
    """Initialize session state variables"""
    if 'chatbot' not in st.session_state:
        st.session_state.chatbot = MultimodalAgenticRAGChatbot()
    
    if 'messages' not in st.session_state:
        st.session_state.messages = []
    
    if 'session_id' not in st.session_state:
        st.session_state.session_id = str(uuid.uuid4())
    
    if 'uploaded_document' not in st.session_state:
        st.session_state.uploaded_document = None
    
    if 'document_processed' not in st.session_state:
        st.session_state.document_processed = False

def display_header():
    """Display the main header and introduction"""
    st.markdown('<h1 class="main-header">🤖 CA Assistant - Your Intelligent Legal, Tax & Business Advisor</h1>', unsafe_allow_html=True)
    
    st.markdown("""
    <div class="feature-box">
        <h3>🎯 RAG-First Intelligence</h3>
        <p>This chatbot prioritizes domain knowledge retrieval for document-related queries with intelligent fallback to normal LLM when documents are irrelevant to your questions.</p>
    </div>
    """, unsafe_allow_html=True)

def display_sidebar():
    """Display sidebar with features and information"""
    with st.sidebar:
        st.header("🚀 Key Features")
        
        st.markdown("""
        **RAG Prioritization Strategy:**
        - 🥇 **Tier 1**: RAG Priority (High relevance > 0.7)
        - 🥈 **Tier 2**: Hybrid Approach (Medium relevance 0.3-0.7)  
        - 🥉 **Tier 3**: LLM-Only Fallback (Low relevance < 0.3)
        """)
        
        st.markdown("---")
        
        st.markdown("""
        **Supported Documents:**
        - 📄 PDF files
        - 📝 Word documents (DOCX)
        - 📊 Excel files (XLSX)
        - 🖼️ Images (JPG, PNG) with OCR
        - 📋 Text files (TXT)
        """)
        
        st.markdown("---")
        
        st.markdown("""
        **Expertise Areas:**
        - 💰 Tax Planning & Compliance
        - ⚖️ Legal & Regulatory Matters
        - 🏢 Business Registration & Structure
        - 📊 Financial Analysis
        - 🔍 Document Analysis
        """)
        
        # Session information
        if st.session_state.get('messages'):
            st.markdown("---")
            st.markdown("**Session Info:**")
            st.write(f"Messages: {len(st.session_state.messages)}")
            st.write(f"Session ID: {st.session_state.session_id[:8]}...")

def handle_file_upload():
    """Handle file upload and processing"""
    st.markdown('<div class="upload-section">', unsafe_allow_html=True)
    st.subheader("📁 Upload Document for Analysis")
    
    uploaded_file = st.file_uploader(
        "Choose a document to analyze",
        type=['pdf', 'docx', 'xlsx', 'jpg', 'png', 'txt'],
        help="Upload documents for RAG-prioritized analysis. The system will assess relevance and choose the best approach."
    )
    
    if uploaded_file is not None:
        if st.session_state.uploaded_document != uploaded_file.name or not st.session_state.document_processed:
            with st.spinner("🔄 Processing document and assessing relevance..."):
                try:
                    # Process document
                    doc_data = st.session_state.chatbot.process_document(
                        uploaded_file, 
                        st.session_state.session_id
                    )
                    
                    st.session_state.uploaded_document = uploaded_file.name
                    st.session_state.document_processed = True
                    
                    # Display document info
                    relevance_score = doc_data.get('relevance', 0.0)
                    
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Document Type", doc_data.get('type', 'Unknown').upper())
                    with col2:
                        st.metric("Relevance Score", f"{relevance_score:.2f}")
                    with col3:
                        if relevance_score > 0.7:
                            approach = "🥇 RAG Priority"
                            color = "green"
                        elif relevance_score > 0.3:
                            approach = "🥈 Hybrid"
                            color = "orange"
                        else:
                            approach = "🥉 LLM Fallback"
                            color = "red"
                        st.markdown(f"**Approach:** <span style='color:{color}'>{approach}</span>", unsafe_allow_html=True)
                    
                    st.success(f"✅ Document '{uploaded_file.name}' processed successfully!")
                    
                    # Show processing strategy explanation
                    if relevance_score > 0.7:
                        st.info("🎯 **RAG Priority Mode**: This document has high relevance to CA domain. I'll prioritize my knowledge base with document context.")
                    elif relevance_score > 0.3:
                        st.info("⚖️ **Hybrid Mode**: This document has medium relevance. I'll combine knowledge base insights with document analysis.")
                    else:
                        st.warning("🔄 **LLM Fallback Mode**: This document appears outside typical CA domain. I'll analyze it directly while noting limited CA relevance.")
                    
                except Exception as e:
                    st.error(f"❌ Error processing document: {str(e)}")
                    st.session_state.document_processed = False
        else:
            st.info(f"📄 Document '{uploaded_file.name}' is already processed and ready for queries.")
    
    st.markdown('</div>', unsafe_allow_html=True)
    
    return uploaded_file

def display_chat_interface():
    """Display the main chat interface"""
    st.subheader("💬 Chat Interface")
    
    # Display chat history
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
    
    # Chat input
    if prompt := st.chat_input("Ask me anything about tax, legal, business matters, or your uploaded document..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        
        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)
        
        # Generate and display assistant response
        with st.chat_message("assistant"):
            with st.spinner("🤔 Analyzing your query and determining the best approach..."):
                try:
                    # Get uploaded file if available
                    uploaded_file = None
                    if st.session_state.document_processed and st.session_state.uploaded_document:
                        # Create a mock file object for the processed document
                        # In a real implementation, you might want to store the file data
                        pass
                    
                    # Process query with RAG prioritization
                    response = st.session_state.chatbot.process_query(
                        prompt,
                        session_id=st.session_state.session_id,
                        uploaded_file=uploaded_file
                    )
                    
                    st.markdown(response)
                    
                except Exception as e:
                    error_message = f"I encountered an error processing your query: {str(e)}"
                    st.error(error_message)
                    response = error_message
        
        # Add assistant response to chat history
        st.session_state.messages.append({"role": "assistant", "content": response})

def display_example_queries():
    """Display example queries for user guidance"""
    with st.expander("💡 Example Queries", expanded=False):
        st.markdown("""
        **Document-Related Queries (RAG Prioritized):**
        - "What are the tax implications of this invoice?"
        - "Analyze this contract for compliance issues"
        - "Calculate the GST from this bill"
        - "Summarize the key points in this document"
        
        **General CA Queries (Knowledge Base):**
        - "What are the tax deductions available for AY 2024-25?"
        - "How do I register a private limited company?"
        - "What are the GST filing requirements?"
        - "Explain the audit requirements for companies"
        
        **Current Information Queries (Web Search):**
        - "What are the latest GST rates?"
        - "Recent changes in income tax law"
        - "Current interest rates for home loans"
        """)

def main():
    """Main application function"""
    # Initialize session state
    initialize_session_state()
    
    # Display header
    display_header()
    
    # Display sidebar
    display_sidebar()
    
    # Main content area
    col1, col2 = st.columns([2, 1])
    
    with col1:
        # File upload section
        uploaded_file = handle_file_upload()
        
        # Chat interface
        display_chat_interface()
    
    with col2:
        # Example queries
        display_example_queries()
        
        # Clear chat button
        if st.button("🗑️ Clear Chat History", type="secondary"):
            st.session_state.messages = []
            st.session_state.session_id = str(uuid.uuid4())
            st.session_state.uploaded_document = None
            st.session_state.document_processed = False
            st.rerun()
    
    # Footer
    st.markdown("---")
    st.markdown("""
    <div style='text-align: center; color: #666;'>
        <p>🤖 CA Assistant with RAG-First Intelligence | Built with Streamlit & OpenAI</p>
        <p><small>This chatbot prioritizes domain knowledge for relevant documents and falls back to general LLM for irrelevant content.</small></p>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
