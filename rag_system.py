"""
RAG System with Document Context Prioritization
Retrieval-Augmented Generation system that prioritizes domain knowledge
with intelligent document context integration
"""

import os
import logging
from typing import Dict, List, Any, Optional
from dotenv import load_dotenv

try:
    from qdrant_client import QdrantClient
    from qdrant_client.models import Distance, VectorParams
except ImportError:
    print("Warning: qdrant-client not installed. RAG functionality will be limited.")
    QdrantClient = None

from openai import OpenAI

load_dotenv()

class RAGSystem:
    """
    RAG system implementing knowledge base prioritization with document context support
    """
    
    def __init__(self):
        """Initialize RAG system with Qdrant and OpenAI clients"""
        self.openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        self.logger = logging.getLogger(__name__)
        
        # Initialize Qdrant client if available
        self.qdrant_client = None
        if QdrantClient and os.getenv('QDRANT_URL') and os.getenv('QDRANT_API_KEY'):
            try:
                self.qdrant_client = QdrantClient(
                    url=os.getenv('QDRANT_URL'),
                    api_key=os.getenv('QDRANT_API_KEY')
                )
                self.collection_name = "ca_knowledge_base"
                self.logger.info("Qdrant client initialized successfully")
            except Exception as e:
                self.logger.warning(f"Failed to initialize Qdrant client: {str(e)}")
                self.qdrant_client = None
        else:
            self.logger.warning("Qdrant configuration not found. Using fallback mode.")
    
    def query(self, query: str, context: Dict) -> str:
        """
        Standard RAG query without document context
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            RAG-generated response
        """
        try:
            if self.qdrant_client:
                return self._query_with_vector_search(query, context)
            else:
                return self._query_fallback_mode(query, context)
                
        except Exception as e:
            self.logger.error(f"Error in RAG query: {str(e)}")
            return self._query_fallback_mode(query, context)
    
    def query_with_document_context(self, query: str, document_content: str, context: Dict) -> str:
        """
        Enhanced RAG query that prioritizes domain knowledge with document context
        
        Args:
            query: User's question
            document_content: Content from uploaded document
            context: Session context
            
        Returns:
            RAG response prioritizing knowledge base with document context
        """
        try:
            if self.qdrant_client:
                return self._query_with_document_and_vectors(query, document_content, context)
            else:
                return self._query_with_document_fallback(query, document_content, context)
                
        except Exception as e:
            self.logger.error(f"Error in RAG query with document context: {str(e)}")
            return self._query_with_document_fallback(query, document_content, context)
    
    def _query_with_vector_search(self, query: str, context: Dict) -> str:
        """
        Query using vector search in Qdrant
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            RAG response using vector search
        """
        # Generate query embedding
        query_embedding = self.get_embedding(query)
        
        # Search similar documents in knowledge base
        search_results = self.qdrant_client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding,
            limit=5
        )
        
        # Extract content from search results
        context_docs = []
        if search_results:
            context_docs = [result.payload.get('content', '') for result in search_results]
        
        # Generate response using retrieved context
        return self.generate_response(query, context_docs, context)
    
    def _query_with_document_and_vectors(self, query: str, document_content: str, context: Dict) -> str:
        """
        Query using both vector search and document context
        
        Args:
            query: User's question
            document_content: Document content
            context: Session context
            
        Returns:
            RAG response with document context prioritization
        """
        # Generate query embedding
        query_embedding = self.get_embedding(query)
        
        # Search similar documents in knowledge base
        search_results = self.qdrant_client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding,
            limit=5
        )
        
        # Extract content from search results
        context_docs = []
        if search_results:
            context_docs = [result.payload.get('content', '') for result in search_results]
        
        # Generate response prioritizing RAG knowledge with document context
        return self.generate_response_with_document_context(
            query, context_docs, document_content, context
        )
    
    def _query_fallback_mode(self, query: str, context: Dict) -> str:
        """
        Fallback query mode when Qdrant is not available
        
        Args:
            query: User's question
            context: Session context
            
        Returns:
            Response using built-in CA knowledge
        """
        # Use built-in CA knowledge for common queries
        ca_knowledge = self._get_builtin_ca_knowledge(query)
        
        if ca_knowledge:
            return self.generate_response(query, [ca_knowledge], context)
        else:
            return self._generate_direct_response(query, context)
    
    def _query_with_document_fallback(self, query: str, document_content: str, context: Dict) -> str:
        """
        Fallback query with document when Qdrant is not available
        
        Args:
            query: User's question
            document_content: Document content
            context: Session context
            
        Returns:
            Response using built-in knowledge and document
        """
        ca_knowledge = self._get_builtin_ca_knowledge(query)
        
        if ca_knowledge:
            return self.generate_response_with_document_context(
                query, [ca_knowledge], document_content, context
            )
        else:
            return self._generate_direct_response_with_document(query, document_content, context)
    
    def get_embedding(self, text: str) -> List[float]:
        """
        Generate embedding for text using OpenAI
        
        Args:
            text: Text to embed
            
        Returns:
            Embedding vector
        """
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model="text-embedding-ada-002"
            )
            return response.data[0].embedding
        except Exception as e:
            self.logger.error(f"Error generating embedding: {str(e)}")
            return []
    
    def generate_response(self, query: str, context_docs: List[str], context: Dict) -> str:
        """
        Generate response using RAG context
        
        Args:
            query: User's question
            context_docs: Retrieved documents from knowledge base
            context: Session context
            
        Returns:
            Generated response
        """
        # Prepare context from retrieved documents
        rag_context = self.format_rag_docs(context_docs)
        conversation_context = self.format_conversation_context(context)
        
        prompt = f"""You are a professional CA assistant with expertise in tax, legal, and business matters.

KNOWLEDGE BASE INFORMATION:
{rag_context}

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Please provide a comprehensive answer based on the knowledge base information. If the knowledge base doesn't contain relevant information, clearly state this and provide general CA guidance where appropriate."""
        
        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Provide accurate, helpful advice based on the provided knowledge base."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            self.logger.error(f"Error generating response: {str(e)}")
            return "I'm experiencing technical difficulties. Please try again."

    def generate_response_with_document_context(self, query: str, rag_docs: List[str],
                                               document_content: str, context: Dict) -> str:
        """
        Generate response prioritizing RAG knowledge with document as supplementary context

        Args:
            query: User's question
            rag_docs: Retrieved documents from knowledge base
            document_content: Content from uploaded document
            context: Session context

        Returns:
            Generated response with RAG prioritization
        """
        # Prepare enhanced prompt that prioritizes RAG knowledge
        rag_context = self.format_rag_docs(rag_docs)
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant with expertise in tax, legal, and business matters.

INSTRUCTIONS:
1. PRIMARILY use information from the Knowledge Base (most authoritative)
2. Use the uploaded document as SUPPLEMENTARY context only
3. If Knowledge Base has relevant information, lead with that
4. Cross-reference document details with established knowledge
5. If document contradicts knowledge base, note the discrepancy
6. Always maintain professional CA advisory tone

KNOWLEDGE BASE INFORMATION:
{rag_context}

UPLOADED DOCUMENT CONTEXT:
{document_content[:1500]}...

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Please provide a comprehensive answer that:
- Leads with authoritative knowledge base information
- References specific document details where relevant
- Explains any discrepancies between sources
- Provides actionable CA advice"""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Always prioritize established knowledge base information over document content when providing advice."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error generating response with document context: {str(e)}")
            return "I'm experiencing technical difficulties processing your document query. Please try again."

    def format_rag_docs(self, docs: List[str]) -> str:
        """Format RAG documents for prompt"""
        if not docs:
            return "No relevant information found in knowledge base."

        formatted_docs = ""
        for i, doc in enumerate(docs, 1):
            if doc.strip():
                formatted_docs += f"Source {i}: {doc[:500]}...\n\n"

        return formatted_docs if formatted_docs else "No relevant information found in knowledge base."

    def format_conversation_context(self, context: Dict) -> str:
        """Format conversation context for prompt"""
        if not context.get('context'):
            return "No previous context"

        recent_context = context['context'][-3:]  # Last 3 exchanges
        formatted_context = ""
        for exchange in recent_context:
            formatted_context += f"Q: {exchange['query']}\nA: {exchange['response'][:200]}...\n\n"

        return formatted_context if formatted_context else "No previous context"

    def _get_builtin_ca_knowledge(self, query: str) -> Optional[str]:
        """
        Get built-in CA knowledge for common queries when Qdrant is not available

        Args:
            query: User's question

        Returns:
            Relevant CA knowledge or None
        """
        query_lower = query.lower()

        # Basic CA knowledge base for fallback
        knowledge_base = {
            'tax deduction': """Common tax deductions under Income Tax Act:
            - Section 80C: Life insurance, PPF, ELSS (up to ₹1.5 lakh)
            - Section 80D: Health insurance premiums
            - Section 24: Home loan interest
            - Section 80E: Education loan interest
            - Section 80G: Donations to charitable organizations""",

            'gst': """GST (Goods and Services Tax) basics:
            - Standard rate: 18%
            - Essential goods: 5%
            - Luxury items: 28%
            - Registration required if turnover > ₹40 lakh (₹20 lakh for special states)
            - Monthly/quarterly returns filing required""",

            'company registration': """Company registration process:
            1. Obtain Digital Signature Certificate (DSC)
            2. Apply for Director Identification Number (DIN)
            3. Reserve company name
            4. File incorporation documents with ROC
            5. Obtain Certificate of Incorporation
            6. Apply for PAN and TAN""",

            'itr filing': """ITR filing requirements:
            - Due date: July 31st for individuals
            - Required documents: Form 16, bank statements, investment proofs
            - Different ITR forms for different income sources
            - Late filing penalty: ₹5,000 (₹1,000 for income < ₹5 lakh)"""
        }

        for keyword, knowledge in knowledge_base.items():
            if keyword in query_lower:
                return knowledge

        return None

    def _generate_direct_response(self, query: str, context: Dict) -> str:
        """Generate direct response without RAG context"""
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant. Answer the following query based on your knowledge of tax, legal, and business matters in India.

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Provide a helpful response with accurate CA guidance."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant specializing in Indian tax, legal, and business matters."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=800
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error generating direct response: {str(e)}")
            return "I'm experiencing technical difficulties. Please try again later."

    def _generate_direct_response_with_document(self, query: str, document_content: str, context: Dict) -> str:
        """Generate direct response with document context"""
        conversation_context = self.format_conversation_context(context)

        prompt = f"""You are a professional CA assistant. Answer the query based on the document content and your CA expertise.

DOCUMENT CONTENT:
{document_content[:2000]}...

USER QUERY: {query}

PREVIOUS CONVERSATION CONTEXT:
{conversation_context}

Provide analysis based on the document and your CA knowledge."""

        try:
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[
                    {"role": "system", "content": "You are a professional CA assistant. Analyze documents with your expertise."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )

            return response.choices[0].message.content

        except Exception as e:
            self.logger.error(f"Error generating direct response with document: {str(e)}")
            return "I'm experiencing technical difficulties analyzing the document. Please try again."


if __name__ == "__main__":
    # Simple test
    rag_system = RAGSystem()
    print("RAG System initialized successfully!")

    # Test basic query
    response = rag_system.query("What are tax deductions?", {})
    print(f"Response: {response[:200]}...")
